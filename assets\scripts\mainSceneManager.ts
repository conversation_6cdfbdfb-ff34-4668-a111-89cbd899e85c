// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { globalVariables } from './utils/GlobalVariables';

import Request from './apis/api';
import { initNativeJs } from './utils/bridge';
// import GlobalDialog from './globalComponet/GlobalDialog';
import { $_share, $_setShareInfo, generateUUID } from './utils/generalUtil';
import { getFingerprint } from './utils/storage';
import ToastManager from './globalComponet/ToastManager';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  ruleComponent: cc.Node = null;

  @property(cc.Node)
  unrealized: cc.Node = null;

  @property(cc.Node)
  bgImg: cc.Node = null;

  @property(cc.Node)
  Basic: cc.Node = null;

  @property(cc.Node)
  ToastNode: cc.Node = null;
  // 冷却时间，单位：秒
  cooldownTime: 1;

  unrealizedBtn() {
    console.log('touch');

    this.unrealized.active = true;
    // this.unrealized.opacity

    setTimeout(() => {
      this.unrealized.active = false;
    }, 2000);
    //this.unrealizedactive = false;
  }

  onLoad() {
    // 初始化用户UUID，如果localStorage中已有则不重新生成
    const userUUID = getFingerprint();
    globalVariables.userUUID = userUUID; // 保存到全局变量
    cc.log('用户UUID:', userUUID);

    initNativeJs(
      res => {
        console.log('init native:', res);
        $_setShareInfo(globalVariables.shareInfo);
      },
      err => {
        console.log('init native fail:', err);
      }
    );
    if (!globalVariables.ifInitCloudData) {
      // 加载到主场景，可以先执行 云函数初始化，以及初始化 全局变量当中云函数的代码
      // 客户要求不需登录，此处默认用户已登录，用前端生成的用户UUID去查询关卡进度
      globalVariables.loggedIn = true;
      Request.getPassLevelData().then((res: any) => {
        globalVariables.passLevelArray = res.passCountArray;
        globalVariables.passTimeArray = res.passTimeArray;

        // 如果返回的数据是passCountArray[1,0,0],passTimeArray[5,0,0]，需保证关卡选择页面下一关可点击
        let nextLevelIndex = globalVariables.passLevelArray.findIndex(
          item => item == 0
        );
        if (
          globalVariables.passTimeArray[nextLevelIndex] != -1 &&
          globalVariables.passTimeArray[nextLevelIndex] != 0
        ) {
          globalVariables.passLevelArray[nextLevelIndex] = 1;
          globalVariables.currentLevel = nextLevelIndex + 1;
        }
      });
      // Request.checkSession()
      //   .then((res: any) => {
      //     if (res.ok) {
      //       globalVariables.loggedIn = true;
      //       Request.getPassLevelData().then((res: any) => {
      //         globalVariables.passLevelArray = res.passCountArray;
      //         globalVariables.passTimeArray = res.passTimeArray;
      //       });
      //     } else {
      //       globalVariables.loggedIn = false;
      //     }
      //   })
      //   .catch(err => {
      //     ToastManager.instance.show(err, 2); // 提示信息，持续显示 2 秒
      //   });
    }

    globalVariables.ifInitCloudData = true;

    // 获取背景图，确保适应屏幕
    const bgSprite = this.bgImg.getComponent(cc.Sprite);
    bgSprite.node.width = cc.view.getVisibleSize().width; // 背景宽度适应屏幕
    bgSprite.node.height = cc.view.getVisibleSize().height; // 背景高度适应屏幕
  }

  start() {
    // if (globalVariables.isFirstLoadGame) {
    //   this.ruleComponent.active = true;
    //   globalVariables.isFirstLoadGame = false;
    // }
  }

  // update (dt) {}
}
